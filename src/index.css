@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Light theme - hell/grau/weiß */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 263 70% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 263 70% 60%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --radius: 0.75rem;

    /* Light theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 40% 98%));
    --gradient-accent: linear-gradient(145deg, hsl(var(--accent)), hsl(210 40% 94%));
    
    /* Light theme shadows */
    --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -2px hsl(0 0% 0% / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 263 70% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 263 70% 60%;

    --secondary: 240 4.8% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4.8% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 4.8% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 263 70% 50%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    /* Dark theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-card: linear-gradient(145deg, hsl(240 10% 4.5%), hsl(240 8% 6%));
    --gradient-accent: linear-gradient(145deg, hsl(var(--accent)), hsl(240 4.8% 18%));
    
    /* Dark theme shadows */
    --shadow-sm: 0 1px 2px 0 hsl(240 10% 3.9% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(240 10% 3.9% / 0.1), 0 2px 4px -1px hsl(240 10% 3.9% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(240 10% 3.9% / 0.1), 0 4px 6px -2px hsl(240 10% 3.9% / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}