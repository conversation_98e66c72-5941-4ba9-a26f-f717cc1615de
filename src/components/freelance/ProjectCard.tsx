import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full h-full flex flex-col">
      {/* Header Section - Fixed Height */}
      <CardHeader className="pb-3 relative flex-shrink-0">
        <Badge
          variant="outline"
          className={`${STATUS_COLORS[project.status]} text-xs border absolute top-3 right-3 z-10`}
        >
          {STATUS_LABELS[project.status]}
        </Badge>

        {/* Action Buttons */}
        <div className="flex gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity absolute top-8 sm:top-12 right-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(project.id)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-destructive/10 text-destructive"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>

        {/* Project Title - No Truncation */}
        <div className="pr-16 sm:pr-20 pt-2">
          <h3 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors break-words leading-tight min-h-[3rem] flex items-start">
            {project.project_name}
          </h3>
        </div>

        {/* Company and Contact Info - Consistent Height */}
        <div className="space-y-2 pr-16 sm:pr-20 min-h-[2.5rem] flex flex-col justify-start">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <span className="break-words">{project.company_name}</span>
            </div>
            {project.contact_person && (
              <div className="flex items-center gap-1">
                <User className="h-4 w-4 flex-shrink-0" />
                <span className="break-words">{project.contact_person}</span>
              </div>
            )}
          </div>
        </div>

        {/* Work Location Badge - Consistent Height */}
        <div className="min-h-[1.5rem] flex items-start pr-16 sm:pr-20">
          {project.work_location_type && (
            <Badge variant="outline" className={`${WORK_LOCATION_COLORS[project.work_location_type]} text-xs`}>
              {WORK_LOCATION_LABELS[project.work_location_type]}
              {project.remote_percentage && ` (${project.remote_percentage}%)`}
            </Badge>
          )}
        </div>
      </CardHeader>

      {/* Content Section - Flexible Height with Consistent Alignment */}
      <CardContent className="pt-0 flex-1 flex flex-col">
        <div className="space-y-3 flex-1">
          {/* Application Date and Budget - Consistent Height */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-sm min-h-[1.25rem]">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <span className="break-words">Bewerbung: {formatDate(project.application_date)}</span>
            </div>
            <div className="text-muted-foreground">
              {project.budget_range && (
                <>
                  <span className="font-medium">Budget:</span> <span className="break-words">{project.budget_range}</span>
                </>
              )}
            </div>
          </div>

          {/* Project Dates - Consistent Height */}
          <div className="min-h-[1.25rem] flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm text-muted-foreground">
            {project.project_start_date && (
              <span className="break-words">Start: {formatDate(project.project_start_date)}</span>
            )}
            {project.project_end_date && (
              <span className="break-words">Ende: {formatDate(project.project_end_date)}</span>
            )}
          </div>

          {/* Skills Section - Consistent Height */}
          <div className="min-h-[2rem] flex items-start">
            {project.required_skills && project.required_skills.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {project.required_skills.slice(0, isExpanded ? undefined : 3).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {skill}
                  </Badge>
                ))}
                {project.required_skills.length > 3 && !isExpanded && (
                  <Badge
                    variant="outline"
                    className="text-xs cursor-pointer hover:bg-accent"
                    onClick={() => setIsExpanded(true)}
                  >
                    +{project.required_skills.length - 3} weitere
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Contact Information - Consistent Height */}
          <div className="space-y-2 min-h-[2.5rem] flex flex-col justify-start">
            {project.contact_email && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4 flex-shrink-0" />
                <a
                  href={`mailto:${project.contact_email}`}
                  className="hover:text-primary transition-colors break-words"
                >
                  {project.contact_email}
                </a>
              </div>
            )}

            {project.contact_phone && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4 flex-shrink-0" />
                <span className="break-words">{project.contact_phone}</span>
              </div>
            )}
          </div>

          {/* Project Description - Flexible Height */}
          {project.project_description && (
            <div className="text-sm text-muted-foreground flex-1">
              <p className={`${!isExpanded ? 'line-clamp-3' : ''} break-words`}>
                {project.project_description}
              </p>
              {project.project_description.length > 150 && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-auto p-0 text-xs text-primary mt-1"
                >
                  {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};