import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full max-w-full">
      <CardHeader className="pb-3 relative">
        <Badge
          variant="outline"
          className={`${STATUS_COLORS[project.status]} text-xs border absolute top-3 right-3 z-10`}
        >
          {STATUS_LABELS[project.status]}
        </Badge>
        <div className="flex flex-col space-y-3">
          <div className="space-y-2 pr-16 sm:pr-20">
            <div className="pt-2">
              <h3 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors line-clamp-2 break-words">
                {project.project_name}
              </h3>
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1 min-w-0">
                <Building2 className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{project.company_name}</span>
              </div>
              {project.contact_person && (
                <div className="flex items-center gap-1 min-w-0">
                  <User className="h-4 w-4 flex-shrink-0" />
                  <span className="truncate">{project.contact_person}</span>
                </div>
              )}
            </div>

            {project.work_location_type && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={`${WORK_LOCATION_COLORS[project.work_location_type]} text-xs`}>
                  {WORK_LOCATION_LABELS[project.work_location_type]}
                  {project.remote_percentage && ` (${project.remote_percentage}%)`}
                </Badge>
              </div>
            )}
          </div>

          <div className="flex gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity absolute top-8 sm:top-12 right-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(project)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(project)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
            >
              <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(project.id)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-destructive/10 text-destructive"
            >
              <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-sm">
            <div className="flex items-center gap-2 text-muted-foreground min-w-0">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">Bewerbung: {formatDate(project.application_date)}</span>
            </div>
            {project.budget_range && (
              <div className="text-muted-foreground min-w-0">
                <span className="font-medium">Budget:</span> <span className="truncate">{project.budget_range}</span>
              </div>
            )}
          </div>

          {(project.project_start_date || project.project_end_date) && (
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm text-muted-foreground">
              {project.project_start_date && (
                <span className="truncate">Start: {formatDate(project.project_start_date)}</span>
              )}
              {project.project_end_date && (
                <span className="truncate">Ende: {formatDate(project.project_end_date)}</span>
              )}
            </div>
          )}

          {project.required_skills && project.required_skills.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {project.required_skills.slice(0, isExpanded ? undefined : 3).map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {project.required_skills.length > 3 && !isExpanded && (
                <Badge 
                  variant="outline" 
                  className="text-xs cursor-pointer hover:bg-accent"
                  onClick={() => setIsExpanded(true)}
                >
                  +{project.required_skills.length - 3} weitere
                </Badge>
              )}
            </div>
          )}

          {project.contact_email && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground min-w-0">
              <Mail className="h-4 w-4 flex-shrink-0" />
              <a
                href={`mailto:${project.contact_email}`}
                className="hover:text-primary transition-colors truncate"
              >
                {project.contact_email}
              </a>
            </div>
          )}

          {project.contact_phone && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground min-w-0">
              <Phone className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{project.contact_phone}</span>
            </div>
          )}

          {project.project_description && (
            <div className="text-sm text-muted-foreground">
              <p className={`${!isExpanded ? 'line-clamp-2' : ''} break-words`}>
                {project.project_description}
              </p>
              {project.project_description.length > 150 && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-auto p-0 text-xs text-primary"
                >
                  {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};