import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group">
      <CardHeader className="pb-3 relative">
        <Badge 
          variant="outline" 
          className={`${STATUS_COLORS[project.status]} text-xs border absolute top-4 right-4`}
        >
          {STATUS_LABELS[project.status]}
        </Badge>
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1 pr-20">
            <div className="pt-3">
              <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors">
                {project.project_name}
              </h3>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                <span>{project.company_name}</span>
              </div>
              {project.contact_person && (
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>{project.contact_person}</span>
                </div>
              )}
        </div>
        
        {project.work_location_type && (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={WORK_LOCATION_COLORS[project.work_location_type]}>
              {WORK_LOCATION_LABELS[project.work_location_type]}
              {project.remote_percentage && ` (${project.remote_percentage}%)`}
            </Badge>
          </div>
        )}
      </div>

          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity absolute top-12 right-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onView(project)}
              className="h-8 w-8 p-0 hover:bg-primary/10"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onEdit(project)}
              className="h-8 w-8 p-0 hover:bg-primary/10"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onDelete(project.id)}
              className="h-8 w-8 p-0 hover:bg-destructive/10 text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Bewerbung: {formatDate(project.application_date)}</span>
            </div>
            {project.budget_range && (
              <div className="text-muted-foreground">
                <span className="font-medium">Budget:</span> {project.budget_range}
              </div>
            )}
          </div>

          {(project.project_start_date || project.project_end_date) && (
            <div className="flex gap-4 text-sm text-muted-foreground">
              {project.project_start_date && (
                <span>Start: {formatDate(project.project_start_date)}</span>
              )}
              {project.project_end_date && (
                <span>Ende: {formatDate(project.project_end_date)}</span>
              )}
            </div>
          )}

          {project.required_skills && project.required_skills.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {project.required_skills.slice(0, isExpanded ? undefined : 3).map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {project.required_skills.length > 3 && !isExpanded && (
                <Badge 
                  variant="outline" 
                  className="text-xs cursor-pointer hover:bg-accent"
                  onClick={() => setIsExpanded(true)}
                >
                  +{project.required_skills.length - 3} weitere
                </Badge>
              )}
            </div>
          )}

          {project.contact_email && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Mail className="h-4 w-4" />
              <a 
                href={`mailto:${project.contact_email}`}
                className="hover:text-primary transition-colors"
              >
                {project.contact_email}
              </a>
            </div>
          )}

          {project.contact_phone && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Phone className="h-4 w-4" />
              <span>{project.contact_phone}</span>
            </div>
          )}

          {project.project_description && (
            <div className="text-sm text-muted-foreground">
              <p className={`${!isExpanded ? 'line-clamp-2' : ''}`}>
                {project.project_description}
              </p>
              {project.project_description.length > 150 && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-auto p-0 text-xs text-primary"
                >
                  {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};