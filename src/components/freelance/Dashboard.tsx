import { useState } from 'react';
import { useFreelanceProjects } from '@/hooks/useFreelanceProjects';
import { ProjectCard } from './ProjectCard';
import { ProjectForm } from './ProjectForm';
import { FreelanceProject } from '@/types/freelance';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Search, Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

export const Dashboard = () => {
  const { projects, isLoading, createProject, updateProject, deleteProject } = useFreelanceProjects();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<FreelanceProject | null>(null);
  const [viewingProject, setViewingProject] = useState<FreelanceProject | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.company_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleCreateProject = async (data: any) => {
    try {
      await createProject(data);
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  const handleUpdateProject = async (data: any) => {
    if (editingProject) {
      try {
        await updateProject({ id: editingProject.id, ...data });
        setEditingProject(null);
      } catch (error) {
        console.error('Error updating project:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Lade Projekte...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 sm:p-6 max-w-full overflow-x-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Freelancify
            </h1>
            <p className="text-muted-foreground mt-2 text-sm sm:text-base">
              Verwalte deine Projektanfragen und Bewerbungen
            </p>
          </div>
          <Button
            onClick={() => setIsFormOpen(true)}
            className="bg-primary hover:bg-primary/90 w-full sm:w-auto flex-shrink-0"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Neues Projekt</span>
            <span className="xs:hidden">Neu</span>
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-primary">{projects.length}</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Gesamt</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-blue-400">
              {projects.filter(p => p.status === 'application_sent').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Beworben</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-success">
              {projects.filter(p => p.status === 'offer_received').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Zusagen</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-warning">
              {projects.filter(p => p.status === 'interview_scheduled' || p.status === 'interview_completed').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Interviews</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Projekte durchsuchen..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[180px] lg:w-[200px]">
              <Filter className="h-4 w-4 mr-2 flex-shrink-0" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle Status</SelectItem>
              {Object.entries(STATUS_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Results count */}
        <div className="flex items-center gap-2 mb-4">
          <Badge variant="outline">
            {filteredProjects.length} von {projects.length} Projekten
          </Badge>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length === 0 ? (
          <div className="text-center py-8 sm:py-12">
            <div className="text-muted-foreground mb-4 text-sm sm:text-base px-4">
              {projects.length === 0 ? 'Noch keine Projekte vorhanden' : 'Keine Projekte gefunden'}
            </div>
            <Button onClick={() => setIsFormOpen(true)} variant="outline" className="mx-4">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden xs:inline">Erstes Projekt hinzufügen</span>
              <span className="xs:hidden">Projekt hinzufügen</span>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onEdit={setEditingProject}
                onDelete={deleteProject}
                onView={setViewingProject}
              />
            ))}
          </div>
        )}

        {/* Create Project Dialog */}
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[95vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Neues Projekt hinzufügen</DialogTitle>
            </DialogHeader>
            <ProjectForm onClose={() => setIsFormOpen(false)} onSubmit={handleCreateProject} />
          </DialogContent>
        </Dialog>

        {/* Edit Project Dialog */}
        <Dialog open={!!editingProject} onOpenChange={() => setEditingProject(null)}>
          <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[95vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Projekt bearbeiten</DialogTitle>
            </DialogHeader>
            {editingProject && (
              <ProjectForm
                onClose={() => setEditingProject(null)}
                initialProject={editingProject}
                onSubmit={handleUpdateProject}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* View Project Dialog */}
        <Dialog open={!!viewingProject} onOpenChange={() => setViewingProject(null)}>
          <DialogContent className="max-w-[95vw] sm:max-w-3xl max-h-[95vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Projekt Details</DialogTitle>
            </DialogHeader>
            {viewingProject && (
              <div className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="min-w-0">
                    <h3 className="font-semibold text-sm text-muted-foreground mb-1">Projektname</h3>
                    <p className="text-foreground break-words">{viewingProject.project_name}</p>
                  </div>
                  <div className="min-w-0">
                    <h3 className="font-semibold text-sm text-muted-foreground mb-1">Firma</h3>
                    <p className="text-foreground break-words">{viewingProject.company_name}</p>
                  </div>
                  {viewingProject.contact_person && (
                    <div className="min-w-0">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Ansprechpartner</h3>
                      <p className="text-foreground break-words">{viewingProject.contact_person}</p>
                    </div>
                  )}
                  {viewingProject.contact_email && (
                    <div className="min-w-0">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">E-Mail</h3>
                      <p className="text-foreground break-all">{viewingProject.contact_email}</p>
                    </div>
                  )}
                  {viewingProject.contact_phone && (
                    <div className="min-w-0">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Telefon</h3>
                      <p className="text-foreground break-words">{viewingProject.contact_phone}</p>
                    </div>
                  )}
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-1">Status</h3>
                    <Badge variant="outline" className={STATUS_COLORS[viewingProject.status]}>
                      {STATUS_LABELS[viewingProject.status]}
                    </Badge>
                  </div>
                  {viewingProject.work_location_type && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Arbeitsmodell</h3>
                      <Badge variant="outline" className={WORK_LOCATION_COLORS[viewingProject.work_location_type]}>
                        {WORK_LOCATION_LABELS[viewingProject.work_location_type]}
                        {viewingProject.remote_percentage && ` (${viewingProject.remote_percentage}%)`}
                      </Badge>
                    </div>
                  )}
                  {viewingProject.budget_range && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Budget</h3>
                      <p className="text-foreground">{viewingProject.budget_range}</p>
                    </div>
                  )}
                  {viewingProject.project_start_date && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Projektstart</h3>
                      <p className="text-foreground">{format(new Date(viewingProject.project_start_date), 'dd.MM.yyyy', { locale: de })}</p>
                    </div>
                  )}
                  {viewingProject.project_end_date && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Projektende</h3>
                      <p className="text-foreground">{format(new Date(viewingProject.project_end_date), 'dd.MM.yyyy', { locale: de })}</p>
                    </div>
                  )}
                  {viewingProject.source && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Quelle</h3>
                      <p className="text-foreground">{viewingProject.source}</p>
                    </div>
                  )}
                </div>
                
                {viewingProject.project_description && (
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">Projektbeschreibung</h3>
                    <p className="text-foreground text-sm leading-relaxed">{viewingProject.project_description}</p>
                  </div>
                )}
                
                {viewingProject.required_skills && viewingProject.required_skills.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">Benötigte Skills</h3>
                    <div className="flex flex-wrap gap-2">
                      {viewingProject.required_skills.map((skill, index) => (
                        <Badge key={index} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {viewingProject.application_text && (
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">Bewerbungstext</h3>
                    <p className="text-foreground text-sm leading-relaxed whitespace-pre-wrap">{viewingProject.application_text}</p>
                  </div>
                )}
                
                {viewingProject.notes && (
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">Notizen</h3>
                    <p className="text-foreground text-sm leading-relaxed">{viewingProject.notes}</p>
                  </div>
                )}
                
                {viewingProject.listing_url && (
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">Link zur Ausschreibung</h3>
                    <a 
                      href={viewingProject.listing_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary hover:underline text-sm break-all"
                    >
                      {viewingProject.listing_url}
                    </a>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};